{"ast": null, "code": "import { apiClient } from './api';\nclass CategoryService {\n  constructor() {\n    this.readOnlyBaseUrl = '/api/announcements/categories';\n    // For read operations\n    this.crudBaseUrl = '/api/categories';\n  }\n  // For CRUD operations\n\n  /**\n   * Get all categories with their subcategories\n   */\n  async getCategoriesWithSubcategories() {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/with-subcategories`);\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to fetch categories');\n    }\n  }\n\n  /**\n   * Get all categories (without subcategories)\n   */\n  async getCategories() {\n    try {\n      const response = await apiClient.get(this.readOnlyBaseUrl);\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Failed to fetch categories');\n    }\n  }\n\n  /**\n   * Get a specific category by ID\n   */\n  async getCategory(categoryId) {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/${categoryId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to fetch category');\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  async createCategory(categoryData) {\n    try {\n      const response = await apiClient.post(`${this.crudBaseUrl}/categories`, categoryData);\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Failed to create category');\n    }\n  }\n\n  /**\n   * Update an existing category\n   */\n  async updateCategory(categoryId, categoryData) {\n    try {\n      const response = await apiClient.put(`${this.crudBaseUrl}/categories/${categoryId}`, categoryData);\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || 'Failed to update category');\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  async deleteCategory(categoryId) {\n    try {\n      const response = await apiClient.delete(`${this.crudBaseUrl}/categories/${categoryId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || 'Failed to delete category');\n    }\n  }\n\n  /**\n   * Toggle category status (active/inactive)\n   */\n  async toggleCategoryStatus(categoryId, isActive) {\n    try {\n      // Use activate/deactivate endpoints as per backend routes\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const response = await apiClient.put(`${this.crudBaseUrl}/categories/${categoryId}/${endpoint}`);\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || 'Failed to update category status');\n    }\n  }\n\n  // Subcategory methods\n\n  /**\n   * Get subcategories for a specific category\n   */\n  async getSubcategories(categoryId) {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/${categoryId}/subcategories`);\n      return response.data;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || 'Failed to fetch subcategories');\n    }\n  }\n\n  /**\n   * Get a specific subcategory by ID\n   */\n  async getSubcategory(categoryId, subcategoryId) {\n    try {\n      var _response$data$data;\n      // Note: Backend doesn't have this nested endpoint, using flat endpoint\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/subcategories`);\n      const subcategories = ((_response$data$data = response.data.data) === null || _response$data$data === void 0 ? void 0 : _response$data$data.subcategories) || [];\n      const subcategory = subcategories.find(sub => sub.subcategory_id === subcategoryId);\n      if (!subcategory) {\n        throw new Error('Subcategory not found');\n      }\n      return {\n        success: true,\n        data: {\n          subcategory\n        },\n        message: 'Subcategory retrieved successfully'\n      };\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      throw new Error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.message) || 'Failed to fetch subcategory');\n    }\n  }\n\n  /**\n   * Create a new subcategory\n   */\n  async createSubcategory(categoryId, subcategoryData) {\n    try {\n      // Add category_id to the data\n      const dataWithCategoryId = {\n        ...subcategoryData,\n        category_id: categoryId\n      };\n      const response = await apiClient.post(`${this.crudBaseUrl}/subcategories`, dataWithCategoryId);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response0$data;\n      throw new Error(((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.message) || 'Failed to create subcategory');\n    }\n  }\n\n  /**\n   * Update an existing subcategory\n   */\n  async updateSubcategory(categoryId, subcategoryId, subcategoryData) {\n    try {\n      const response = await apiClient.put(`${this.crudBaseUrl}/subcategories/${subcategoryId}`, subcategoryData);\n      return response.data;\n    } catch (error) {\n      var _error$response1, _error$response1$data;\n      throw new Error(((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.message) || 'Failed to update subcategory');\n    }\n  }\n\n  /**\n   * Delete a subcategory\n   */\n  async deleteSubcategory(categoryId, subcategoryId) {\n    try {\n      const response = await apiClient.delete(`${this.crudBaseUrl}/subcategories/${subcategoryId}`);\n      return response.data;\n    } catch (error) {\n      var _error$response10, _error$response10$dat;\n      throw new Error(((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) || 'Failed to delete subcategory');\n    }\n  }\n\n  /**\n   * Toggle subcategory status (active/inactive)\n   */\n  async toggleSubcategoryStatus(categoryId, subcategoryId, isActive) {\n    try {\n      // Use activate/deactivate endpoints as per backend routes\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const response = await apiClient.put(`${this.crudBaseUrl}/subcategories/${subcategoryId}/${endpoint}`);\n      return response.data;\n    } catch (error) {\n      var _error$response11, _error$response11$dat;\n      throw new Error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.message) || 'Failed to update subcategory status');\n    }\n  }\n\n  /**\n   * Reorder subcategories\n   */\n  async reorderSubcategories(categoryId, subcategoryOrders) {\n    try {\n      // Note: Backend doesn't have this endpoint, implementing with individual updates\n      const promises = subcategoryOrders.map(order => apiClient.put(`${this.crudBaseUrl}/subcategories/${order.subcategory_id}/order`, {\n        display_order: order.display_order\n      }));\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Subcategories reordered successfully'\n      };\n    } catch (error) {\n      var _error$response12, _error$response12$dat;\n      throw new Error(((_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.message) || 'Failed to reorder subcategories');\n    }\n  }\n\n  // Bulk operations\n\n  /**\n   * Bulk update category status\n   */\n  async bulkUpdateCategoryStatus(categoryIds, isActive) {\n    try {\n      // Note: Backend doesn't have bulk endpoints, implementing with individual updates\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const promises = categoryIds.map(id => apiClient.put(`${this.crudBaseUrl}/categories/${id}/${endpoint}`));\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Categories updated successfully'\n      };\n    } catch (error) {\n      var _error$response13, _error$response13$dat;\n      throw new Error(((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : (_error$response13$dat = _error$response13.data) === null || _error$response13$dat === void 0 ? void 0 : _error$response13$dat.message) || 'Failed to bulk update category status');\n    }\n  }\n\n  /**\n   * Bulk delete categories\n   */\n  async bulkDeleteCategories(categoryIds) {\n    try {\n      // Note: Backend doesn't have bulk endpoints, implementing with individual deletes\n      const promises = categoryIds.map(id => apiClient.delete(`${this.crudBaseUrl}/categories/${id}`));\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Categories deleted successfully'\n      };\n    } catch (error) {\n      var _error$response14, _error$response14$dat;\n      throw new Error(((_error$response14 = error.response) === null || _error$response14 === void 0 ? void 0 : (_error$response14$dat = _error$response14.data) === null || _error$response14$dat === void 0 ? void 0 : _error$response14$dat.message) || 'Failed to bulk delete categories');\n    }\n  }\n\n  /**\n   * Export categories data\n   */\n  async exportCategories(format = 'csv') {\n    try {\n      var _categories$data;\n      // Note: Backend doesn't have export endpoint, implementing basic export\n      const categories = await this.getCategoriesWithSubcategories();\n      const data = JSON.stringify(((_categories$data = categories.data) === null || _categories$data === void 0 ? void 0 : _categories$data.categories) || [], null, 2);\n      return new Blob([data], {\n        type: 'application/json'\n      });\n    } catch (error) {\n      var _error$response15, _error$response15$dat;\n      throw new Error(((_error$response15 = error.response) === null || _error$response15 === void 0 ? void 0 : (_error$response15$dat = _error$response15.data) === null || _error$response15$dat === void 0 ? void 0 : _error$response15$dat.message) || 'Failed to export categories data');\n    }\n  }\n\n  /**\n   * Import categories data\n   */\n  async importCategories(file) {\n    try {\n      // Note: Backend doesn't have import endpoint\n      throw new Error('Import functionality not implemented in backend');\n    } catch (error) {\n      var _error$response16, _error$response16$dat;\n      throw new Error(((_error$response16 = error.response) === null || _error$response16 === void 0 ? void 0 : (_error$response16$dat = _error$response16.data) === null || _error$response16$dat === void 0 ? void 0 : _error$response16$dat.message) || 'Failed to import categories data');\n    }\n  }\n\n  /**\n   * Get category usage statistics\n   */\n  async getCategoryStats() {\n    try {\n      var _categories$data2;\n      // Note: Backend doesn't have stats endpoint, implementing basic stats\n      const categories = await this.getCategoriesWithSubcategories();\n      const categoryData = ((_categories$data2 = categories.data) === null || _categories$data2 === void 0 ? void 0 : _categories$data2.categories) || [];\n      return {\n        success: true,\n        data: {\n          totalCategories: categoryData.length,\n          activeCategories: categoryData.filter(c => c.is_active).length,\n          totalSubcategories: categoryData.reduce((sum, c) => {\n            var _c$subcategories;\n            return sum + (((_c$subcategories = c.subcategories) === null || _c$subcategories === void 0 ? void 0 : _c$subcategories.length) || 0);\n          }, 0)\n        }\n      };\n    } catch (error) {\n      var _error$response17, _error$response17$dat;\n      throw new Error(((_error$response17 = error.response) === null || _error$response17 === void 0 ? void 0 : (_error$response17$dat = _error$response17.data) === null || _error$response17$dat === void 0 ? void 0 : _error$response17$dat.message) || 'Failed to fetch category statistics');\n    }\n  }\n}\nexport const categoryService = new CategoryService();\nexport default categoryService;", "map": {"version": 3, "names": ["apiClient", "CategoryService", "constructor", "readOnlyBaseUrl", "crudBaseUrl", "getCategoriesWithSubcategories", "response", "get", "data", "error", "_error$response", "_error$response$data", "Error", "message", "getCategories", "_error$response2", "_error$response2$data", "getCategory", "categoryId", "_error$response3", "_error$response3$data", "createCategory", "categoryData", "post", "_error$response4", "_error$response4$data", "updateCategory", "put", "_error$response5", "_error$response5$data", "deleteCategory", "delete", "_error$response6", "_error$response6$data", "toggleCategoryStatus", "isActive", "endpoint", "_error$response7", "_error$response7$data", "getSubcategories", "_error$response8", "_error$response8$data", "getSubcategory", "subcategoryId", "_response$data$data", "subcategories", "subcategory", "find", "sub", "subcategory_id", "success", "_error$response9", "_error$response9$data", "createSubcategory", "subcategoryData", "dataWithCategoryId", "category_id", "_error$response0", "_error$response0$data", "updateSubcategory", "_error$response1", "_error$response1$data", "deleteSubcategory", "_error$response10", "_error$response10$dat", "toggleSubcategoryStatus", "_error$response11", "_error$response11$dat", "reorderSubcategories", "subcategoryOrders", "promises", "map", "order", "display_order", "Promise", "all", "_error$response12", "_error$response12$dat", "bulkUpdateCategoryStatus", "categoryIds", "id", "_error$response13", "_error$response13$dat", "bulkDeleteCategories", "_error$response14", "_error$response14$dat", "exportCategories", "format", "_categories$data", "categories", "JSON", "stringify", "Blob", "type", "_error$response15", "_error$response15$dat", "importCategories", "file", "_error$response16", "_error$response16$dat", "getCategoryStats", "_categories$data2", "totalCategories", "length", "activeCategories", "filter", "c", "is_active", "totalSubcategories", "reduce", "sum", "_c$subcategories", "_error$response17", "_error$response17$dat", "categoryService"], "sources": ["D:/online-e-bulletin-reactjs/FRONT-VCBA-E-BULLETIN-BOARD/src/services/categoryService.ts"], "sourcesContent": ["import { apiClient } from './api';\n\ninterface Subcategory {\n  subcategory_id?: number;\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  display_order: number;\n  created_at?: string;\n  updated_at?: string;\n}\n\ninterface Category {\n  category_id?: number;\n  name: string;\n  description?: string;\n  color_code: string;\n  is_active: boolean;\n  created_at?: string;\n  updated_at?: string;\n  subcategories?: Subcategory[];\n}\n\ninterface CategoryResponse {\n  success: boolean;\n  data?: {\n    category?: Category;\n    categories?: Category[];\n  };\n  message: string;\n}\n\ninterface SubcategoryResponse {\n  success: boolean;\n  data?: {\n    subcategory?: Subcategory;\n    subcategories?: Subcategory[];\n  };\n  message: string;\n}\n\nclass CategoryService {\n  private readOnlyBaseUrl = '/api/announcements/categories'; // For read operations\n  private crudBaseUrl = '/api/categories'; // For CRUD operations\n\n  /**\n   * Get all categories with their subcategories\n   */\n  async getCategoriesWithSubcategories(): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/with-subcategories`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch categories');\n    }\n  }\n\n  /**\n   * Get all categories (without subcategories)\n   */\n  async getCategories(): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.get(this.readOnlyBaseUrl);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch categories');\n    }\n  }\n\n  /**\n   * Get a specific category by ID\n   */\n  async getCategory(categoryId: number): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/${categoryId}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch category');\n    }\n  }\n\n  /**\n   * Create a new category\n   */\n  async createCategory(categoryData: Omit<Category, 'category_id'>): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.post(`${this.crudBaseUrl}/categories`, categoryData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to create category');\n    }\n  }\n\n  /**\n   * Update an existing category\n   */\n  async updateCategory(categoryId: number, categoryData: Partial<Category>): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.put(`${this.crudBaseUrl}/categories/${categoryId}`, categoryData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to update category');\n    }\n  }\n\n  /**\n   * Delete a category\n   */\n  async deleteCategory(categoryId: number): Promise<CategoryResponse> {\n    try {\n      const response = await apiClient.delete(`${this.crudBaseUrl}/categories/${categoryId}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to delete category');\n    }\n  }\n\n  /**\n   * Toggle category status (active/inactive)\n   */\n  async toggleCategoryStatus(categoryId: number, isActive: boolean): Promise<CategoryResponse> {\n    try {\n      // Use activate/deactivate endpoints as per backend routes\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const response = await apiClient.put(`${this.crudBaseUrl}/categories/${categoryId}/${endpoint}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to update category status');\n    }\n  }\n\n  // Subcategory methods\n\n  /**\n   * Get subcategories for a specific category\n   */\n  async getSubcategories(categoryId: number): Promise<SubcategoryResponse> {\n    try {\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/${categoryId}/subcategories`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch subcategories');\n    }\n  }\n\n  /**\n   * Get a specific subcategory by ID\n   */\n  async getSubcategory(categoryId: number, subcategoryId: number): Promise<SubcategoryResponse> {\n    try {\n      // Note: Backend doesn't have this nested endpoint, using flat endpoint\n      const response = await apiClient.get(`${this.readOnlyBaseUrl}/subcategories`);\n      const subcategories = response.data.data?.subcategories || [];\n      const subcategory = subcategories.find((sub: any) => sub.subcategory_id === subcategoryId);\n      if (!subcategory) {\n        throw new Error('Subcategory not found');\n      }\n      return {\n        success: true,\n        data: { subcategory },\n        message: 'Subcategory retrieved successfully'\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch subcategory');\n    }\n  }\n\n  /**\n   * Create a new subcategory\n   */\n  async createSubcategory(categoryId: number, subcategoryData: Omit<Subcategory, 'subcategory_id'>): Promise<SubcategoryResponse> {\n    try {\n      // Add category_id to the data\n      const dataWithCategoryId = {\n        ...subcategoryData,\n        category_id: categoryId\n      };\n      const response = await apiClient.post(`${this.crudBaseUrl}/subcategories`, dataWithCategoryId);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to create subcategory');\n    }\n  }\n\n  /**\n   * Update an existing subcategory\n   */\n  async updateSubcategory(categoryId: number, subcategoryId: number, subcategoryData: Partial<Subcategory>): Promise<SubcategoryResponse> {\n    try {\n      const response = await apiClient.put(`${this.crudBaseUrl}/subcategories/${subcategoryId}`, subcategoryData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to update subcategory');\n    }\n  }\n\n  /**\n   * Delete a subcategory\n   */\n  async deleteSubcategory(categoryId: number, subcategoryId: number): Promise<SubcategoryResponse> {\n    try {\n      const response = await apiClient.delete(`${this.crudBaseUrl}/subcategories/${subcategoryId}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to delete subcategory');\n    }\n  }\n\n  /**\n   * Toggle subcategory status (active/inactive)\n   */\n  async toggleSubcategoryStatus(categoryId: number, subcategoryId: number, isActive: boolean): Promise<SubcategoryResponse> {\n    try {\n      // Use activate/deactivate endpoints as per backend routes\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const response = await apiClient.put(`${this.crudBaseUrl}/subcategories/${subcategoryId}/${endpoint}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to update subcategory status');\n    }\n  }\n\n  /**\n   * Reorder subcategories\n   */\n  async reorderSubcategories(categoryId: number, subcategoryOrders: { subcategory_id: number; display_order: number }[]): Promise<SubcategoryResponse> {\n    try {\n      // Note: Backend doesn't have this endpoint, implementing with individual updates\n      const promises = subcategoryOrders.map(order =>\n        apiClient.put(`${this.crudBaseUrl}/subcategories/${order.subcategory_id}/order`, {\n          display_order: order.display_order\n        })\n      );\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Subcategories reordered successfully'\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to reorder subcategories');\n    }\n  }\n\n  // Bulk operations\n\n  /**\n   * Bulk update category status\n   */\n  async bulkUpdateCategoryStatus(categoryIds: number[], isActive: boolean): Promise<CategoryResponse> {\n    try {\n      // Note: Backend doesn't have bulk endpoints, implementing with individual updates\n      const endpoint = isActive ? 'activate' : 'deactivate';\n      const promises = categoryIds.map(id =>\n        apiClient.put(`${this.crudBaseUrl}/categories/${id}/${endpoint}`)\n      );\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Categories updated successfully'\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to bulk update category status');\n    }\n  }\n\n  /**\n   * Bulk delete categories\n   */\n  async bulkDeleteCategories(categoryIds: number[]): Promise<CategoryResponse> {\n    try {\n      // Note: Backend doesn't have bulk endpoints, implementing with individual deletes\n      const promises = categoryIds.map(id =>\n        apiClient.delete(`${this.crudBaseUrl}/categories/${id}`)\n      );\n      await Promise.all(promises);\n      return {\n        success: true,\n        message: 'Categories deleted successfully'\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to bulk delete categories');\n    }\n  }\n\n  /**\n   * Export categories data\n   */\n  async exportCategories(format: 'csv' | 'xlsx' | 'json' = 'csv'): Promise<Blob> {\n    try {\n      // Note: Backend doesn't have export endpoint, implementing basic export\n      const categories = await this.getCategoriesWithSubcategories();\n      const data = JSON.stringify(categories.data?.categories || [], null, 2);\n      return new Blob([data], { type: 'application/json' });\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to export categories data');\n    }\n  }\n\n  /**\n   * Import categories data\n   */\n  async importCategories(file: File): Promise<CategoryResponse> {\n    try {\n      // Note: Backend doesn't have import endpoint\n      throw new Error('Import functionality not implemented in backend');\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to import categories data');\n    }\n  }\n\n  /**\n   * Get category usage statistics\n   */\n  async getCategoryStats(): Promise<any> {\n    try {\n      // Note: Backend doesn't have stats endpoint, implementing basic stats\n      const categories = await this.getCategoriesWithSubcategories();\n      const categoryData = categories.data?.categories || [];\n      return {\n        success: true,\n        data: {\n          totalCategories: categoryData.length,\n          activeCategories: categoryData.filter(c => c.is_active).length,\n          totalSubcategories: categoryData.reduce((sum, c) => sum + (c.subcategories?.length || 0), 0)\n        }\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || 'Failed to fetch category statistics');\n    }\n  }\n}\n\nexport const categoryService = new CategoryService();\nexport default categoryService;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AA2CjC,MAAMC,eAAe,CAAC;EAAAC,YAAA;IAAA,KACZC,eAAe,GAAG,+BAA+B;IAAE;IAAA,KACnDC,WAAW,GAAG,iBAAiB;EAAA;EAAE;;EAEzC;AACF;AACA;EACE,MAAMC,8BAA8BA,CAAA,EAA8B;IAChE,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,eAAe,qBAAqB,CAAC;MAClF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,4BAA4B,CAAC;IAChF;EACF;;EAEA;AACF;AACA;EACE,MAAMC,aAAaA,CAAA,EAA8B;IAC/C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,IAAI,CAACJ,eAAe,CAAC;MAC1D,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAM,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIJ,KAAK,CAAC,EAAAG,gBAAA,GAAAN,KAAK,CAACH,QAAQ,cAAAS,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBP,IAAI,cAAAQ,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAI,4BAA4B,CAAC;IAChF;EACF;;EAEA;AACF;AACA;EACE,MAAMI,WAAWA,CAACC,UAAkB,EAA6B;IAC/D,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,eAAe,IAAIe,UAAU,EAAE,CAAC;MAC7E,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAU,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIR,KAAK,CAAC,EAAAO,gBAAA,GAAAV,KAAK,CAACH,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAI,0BAA0B,CAAC;IAC9E;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,cAAcA,CAACC,YAA2C,EAA6B;IAC3F,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMN,SAAS,CAACuB,IAAI,CAAC,GAAG,IAAI,CAACnB,WAAW,aAAa,EAAEkB,YAAY,CAAC;MACrF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIb,KAAK,CAAC,EAAAY,gBAAA,GAAAf,KAAK,CAACH,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAI,2BAA2B,CAAC;IAC/E;EACF;;EAEA;AACF;AACA;EACE,MAAMa,cAAcA,CAACR,UAAkB,EAAEI,YAA+B,EAA6B;IACnG,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMN,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,eAAec,UAAU,EAAE,EAAEI,YAAY,CAAC;MAClG,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIjB,KAAK,CAAC,EAAAgB,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBhB,OAAO,KAAI,2BAA2B,CAAC;IAC/E;EACF;;EAEA;AACF;AACA;EACE,MAAMiB,cAAcA,CAACZ,UAAkB,EAA6B;IAClE,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMN,SAAS,CAAC+B,MAAM,CAAC,GAAG,IAAI,CAAC3B,WAAW,eAAec,UAAU,EAAE,CAAC;MACvF,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAuB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIrB,KAAK,CAAC,EAAAoB,gBAAA,GAAAvB,KAAK,CAACH,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBxB,IAAI,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBpB,OAAO,KAAI,2BAA2B,CAAC;IAC/E;EACF;;EAEA;AACF;AACA;EACE,MAAMqB,oBAAoBA,CAAChB,UAAkB,EAAEiB,QAAiB,EAA6B;IAC3F,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,QAAQ,GAAG,UAAU,GAAG,YAAY;MACrD,MAAM7B,QAAQ,GAAG,MAAMN,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,eAAec,UAAU,IAAIkB,QAAQ,EAAE,CAAC;MAChG,OAAO9B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI1B,KAAK,CAAC,EAAAyB,gBAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,kCAAkC,CAAC;IACtF;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAM0B,gBAAgBA,CAACrB,UAAkB,EAAgC;IACvE,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,eAAe,IAAIe,UAAU,gBAAgB,CAAC;MAC3F,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI7B,KAAK,CAAC,EAAA4B,gBAAA,GAAA/B,KAAK,CAACH,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsB5B,OAAO,KAAI,+BAA+B,CAAC;IACnF;EACF;;EAEA;AACF;AACA;EACE,MAAM6B,cAAcA,CAACxB,UAAkB,EAAEyB,aAAqB,EAAgC;IAC5F,IAAI;MAAA,IAAAC,mBAAA;MACF;MACA,MAAMtC,QAAQ,GAAG,MAAMN,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,eAAe,gBAAgB,CAAC;MAC7E,MAAM0C,aAAa,GAAG,EAAAD,mBAAA,GAAAtC,QAAQ,CAACE,IAAI,CAACA,IAAI,cAAAoC,mBAAA,uBAAlBA,mBAAA,CAAoBC,aAAa,KAAI,EAAE;MAC7D,MAAMC,WAAW,GAAGD,aAAa,CAACE,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACC,cAAc,KAAKN,aAAa,CAAC;MAC1F,IAAI,CAACG,WAAW,EAAE;QAChB,MAAM,IAAIlC,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MACA,OAAO;QACLsC,OAAO,EAAE,IAAI;QACb1C,IAAI,EAAE;UAAEsC;QAAY,CAAC;QACrBjC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIxC,KAAK,CAAC,EAAAuC,gBAAA,GAAA1C,KAAK,CAACH,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBvC,OAAO,KAAI,6BAA6B,CAAC;IACjF;EACF;;EAEA;AACF;AACA;EACE,MAAMwC,iBAAiBA,CAACnC,UAAkB,EAAEoC,eAAoD,EAAgC;IAC9H,IAAI;MACF;MACA,MAAMC,kBAAkB,GAAG;QACzB,GAAGD,eAAe;QAClBE,WAAW,EAAEtC;MACf,CAAC;MACD,MAAMZ,QAAQ,GAAG,MAAMN,SAAS,CAACuB,IAAI,CAAC,GAAG,IAAI,CAACnB,WAAW,gBAAgB,EAAEmD,kBAAkB,CAAC;MAC9F,OAAOjD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI9C,KAAK,CAAC,EAAA6C,gBAAA,GAAAhD,KAAK,CAACH,QAAQ,cAAAmD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjD,IAAI,cAAAkD,qBAAA,uBAApBA,qBAAA,CAAsB7C,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;;EAEA;AACF;AACA;EACE,MAAM8C,iBAAiBA,CAACzC,UAAkB,EAAEyB,aAAqB,EAAEW,eAAqC,EAAgC;IACtI,IAAI;MACF,MAAMhD,QAAQ,GAAG,MAAMN,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,kBAAkBuC,aAAa,EAAE,EAAEW,eAAe,CAAC;MAC3G,OAAOhD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAmD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIjD,KAAK,CAAC,EAAAgD,gBAAA,GAAAnD,KAAK,CAACH,QAAQ,cAAAsD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpD,IAAI,cAAAqD,qBAAA,uBAApBA,qBAAA,CAAsBhD,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;;EAEA;AACF;AACA;EACE,MAAMiD,iBAAiBA,CAAC5C,UAAkB,EAAEyB,aAAqB,EAAgC;IAC/F,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMN,SAAS,CAAC+B,MAAM,CAAC,GAAG,IAAI,CAAC3B,WAAW,kBAAkBuC,aAAa,EAAE,CAAC;MAC7F,OAAOrC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAsD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIpD,KAAK,CAAC,EAAAmD,iBAAA,GAAAtD,KAAK,CAACH,QAAQ,cAAAyD,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBvD,IAAI,cAAAwD,qBAAA,uBAApBA,qBAAA,CAAsBnD,OAAO,KAAI,8BAA8B,CAAC;IAClF;EACF;;EAEA;AACF;AACA;EACE,MAAMoD,uBAAuBA,CAAC/C,UAAkB,EAAEyB,aAAqB,EAAER,QAAiB,EAAgC;IACxH,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,QAAQ,GAAG,UAAU,GAAG,YAAY;MACrD,MAAM7B,QAAQ,GAAG,MAAMN,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,kBAAkBuC,aAAa,IAAIP,QAAQ,EAAE,CAAC;MACtG,OAAO9B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAyD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvD,KAAK,CAAC,EAAAsD,iBAAA,GAAAzD,KAAK,CAACH,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB1D,IAAI,cAAA2D,qBAAA,uBAApBA,qBAAA,CAAsBtD,OAAO,KAAI,qCAAqC,CAAC;IACzF;EACF;;EAEA;AACF;AACA;EACE,MAAMuD,oBAAoBA,CAAClD,UAAkB,EAAEmD,iBAAsE,EAAgC;IACnJ,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,iBAAiB,CAACE,GAAG,CAACC,KAAK,IAC1CxE,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,kBAAkBoE,KAAK,CAACvB,cAAc,QAAQ,EAAE;QAC/EwB,aAAa,EAAED,KAAK,CAACC;MACvB,CAAC,CACH,CAAC;MACD,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAC3B,OAAO;QACLpB,OAAO,EAAE,IAAI;QACbrC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MAAA,IAAAmE,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIjE,KAAK,CAAC,EAAAgE,iBAAA,GAAAnE,KAAK,CAACH,QAAQ,cAAAsE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBpE,IAAI,cAAAqE,qBAAA,uBAApBA,qBAAA,CAAsBhE,OAAO,KAAI,iCAAiC,CAAC;IACrF;EACF;;EAEA;;EAEA;AACF;AACA;EACE,MAAMiE,wBAAwBA,CAACC,WAAqB,EAAE5C,QAAiB,EAA6B;IAClG,IAAI;MACF;MACA,MAAMC,QAAQ,GAAGD,QAAQ,GAAG,UAAU,GAAG,YAAY;MACrD,MAAMmC,QAAQ,GAAGS,WAAW,CAACR,GAAG,CAACS,EAAE,IACjChF,SAAS,CAAC2B,GAAG,CAAC,GAAG,IAAI,CAACvB,WAAW,eAAe4E,EAAE,IAAI5C,QAAQ,EAAE,CAClE,CAAC;MACD,MAAMsC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAC3B,OAAO;QACLpB,OAAO,EAAE,IAAI;QACbrC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MAAA,IAAAwE,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAItE,KAAK,CAAC,EAAAqE,iBAAA,GAAAxE,KAAK,CAACH,QAAQ,cAAA2E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzE,IAAI,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBrE,OAAO,KAAI,uCAAuC,CAAC;IAC3F;EACF;;EAEA;AACF;AACA;EACE,MAAMsE,oBAAoBA,CAACJ,WAAqB,EAA6B;IAC3E,IAAI;MACF;MACA,MAAMT,QAAQ,GAAGS,WAAW,CAACR,GAAG,CAACS,EAAE,IACjChF,SAAS,CAAC+B,MAAM,CAAC,GAAG,IAAI,CAAC3B,WAAW,eAAe4E,EAAE,EAAE,CACzD,CAAC;MACD,MAAMN,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAC3B,OAAO;QACLpB,OAAO,EAAE,IAAI;QACbrC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOJ,KAAU,EAAE;MAAA,IAAA2E,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIzE,KAAK,CAAC,EAAAwE,iBAAA,GAAA3E,KAAK,CAACH,QAAQ,cAAA8E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB5E,IAAI,cAAA6E,qBAAA,uBAApBA,qBAAA,CAAsBxE,OAAO,KAAI,kCAAkC,CAAC;IACtF;EACF;;EAEA;AACF;AACA;EACE,MAAMyE,gBAAgBA,CAACC,MAA+B,GAAG,KAAK,EAAiB;IAC7E,IAAI;MAAA,IAAAC,gBAAA;MACF;MACA,MAAMC,UAAU,GAAG,MAAM,IAAI,CAACpF,8BAA8B,CAAC,CAAC;MAC9D,MAAMG,IAAI,GAAGkF,IAAI,CAACC,SAAS,CAAC,EAAAH,gBAAA,GAAAC,UAAU,CAACjF,IAAI,cAAAgF,gBAAA,uBAAfA,gBAAA,CAAiBC,UAAU,KAAI,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;MACvE,OAAO,IAAIG,IAAI,CAAC,CAACpF,IAAI,CAAC,EAAE;QAAEqF,IAAI,EAAE;MAAmB,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOpF,KAAU,EAAE;MAAA,IAAAqF,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAInF,KAAK,CAAC,EAAAkF,iBAAA,GAAArF,KAAK,CAACH,QAAQ,cAAAwF,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBtF,IAAI,cAAAuF,qBAAA,uBAApBA,qBAAA,CAAsBlF,OAAO,KAAI,kCAAkC,CAAC;IACtF;EACF;;EAEA;AACF;AACA;EACE,MAAMmF,gBAAgBA,CAACC,IAAU,EAA6B;IAC5D,IAAI;MACF;MACA,MAAM,IAAIrF,KAAK,CAAC,iDAAiD,CAAC;IACpE,CAAC,CAAC,OAAOH,KAAU,EAAE;MAAA,IAAAyF,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvF,KAAK,CAAC,EAAAsF,iBAAA,GAAAzF,KAAK,CAACH,QAAQ,cAAA4F,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB1F,IAAI,cAAA2F,qBAAA,uBAApBA,qBAAA,CAAsBtF,OAAO,KAAI,kCAAkC,CAAC;IACtF;EACF;;EAEA;AACF;AACA;EACE,MAAMuF,gBAAgBA,CAAA,EAAiB;IACrC,IAAI;MAAA,IAAAC,iBAAA;MACF;MACA,MAAMZ,UAAU,GAAG,MAAM,IAAI,CAACpF,8BAA8B,CAAC,CAAC;MAC9D,MAAMiB,YAAY,GAAG,EAAA+E,iBAAA,GAAAZ,UAAU,CAACjF,IAAI,cAAA6F,iBAAA,uBAAfA,iBAAA,CAAiBZ,UAAU,KAAI,EAAE;MACtD,OAAO;QACLvC,OAAO,EAAE,IAAI;QACb1C,IAAI,EAAE;UACJ8F,eAAe,EAAEhF,YAAY,CAACiF,MAAM;UACpCC,gBAAgB,EAAElF,YAAY,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAACJ,MAAM;UAC9DK,kBAAkB,EAAEtF,YAAY,CAACuF,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC;YAAA,IAAAK,gBAAA;YAAA,OAAKD,GAAG,IAAI,EAAAC,gBAAA,GAAAL,CAAC,CAAC7D,aAAa,cAAAkE,gBAAA,uBAAfA,gBAAA,CAAiBR,MAAM,KAAI,CAAC,CAAC;UAAA,GAAE,CAAC;QAC7F;MACF,CAAC;IACH,CAAC,CAAC,OAAO9F,KAAU,EAAE;MAAA,IAAAuG,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIrG,KAAK,CAAC,EAAAoG,iBAAA,GAAAvG,KAAK,CAACH,QAAQ,cAAA0G,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBxG,IAAI,cAAAyG,qBAAA,uBAApBA,qBAAA,CAAsBpG,OAAO,KAAI,qCAAqC,CAAC;IACzF;EACF;AACF;AAEA,OAAO,MAAMqG,eAAe,GAAG,IAAIjH,eAAe,CAAC,CAAC;AACpD,eAAeiH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}